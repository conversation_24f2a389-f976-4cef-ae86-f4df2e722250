#!/usr/bin/env python3
"""
Debug script to identify the exact training error.
Run this to get detailed error information and potential fixes.
"""

import os
import sys
import torch
import logging
import traceback
from pathlib import Path

# Add the itas directory to the path
sys.path.insert(0, "/data_x/junkim100/projects/scheming_sae/itas")

# Set up detailed logging
logging.basicConfig(
    level=logging.DEBUG, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


def test_basic_imports():
    """Test that all required modules can be imported."""
    print("🔍 Testing basic imports...")

    try:
        import itas
        from itas import (
            SAEConfig,
            SAETrainer,
            ModelConfig,
            DatasetConfig,
            TrainingConfig,
        )

        print("✅ ITAS imports successful")
        return True
    except Exception as e:
        print(f"❌ Import error: {e}")
        traceback.print_exc()
        return False


def test_model_loading():
    """Test model loading with minimal configuration."""
    print("\n🤖 Testing model loading...")

    try:
        from itas.core.model_loader import UniversalModelLoader
        from itas import ModelConfig

        # Use a smaller model for testing
        model_config = ModelConfig(
            model_name="meta-llama/Llama-3.1-8B-Instruct",
            use_flash_attention=False,  # Disable flash attention for compatibility
            torch_dtype="bfloat16",
            device_map="auto",
            trust_remote_code=False,
        )

        print("  Loading model...")
        model_loader = UniversalModelLoader(model_config)
        model, tokenizer = model_loader.load_model_and_tokenizer()

        print(f"✅ Model loaded successfully")
        print(f"  Model device: {next(model.parameters()).device}")
        print(f"  Model dtype: {next(model.parameters()).dtype}")

        return model, tokenizer, model_loader

    except Exception as e:
        print(f"❌ Model loading error: {e}")
        traceback.print_exc()
        return None, None, None


def test_dataset_loading():
    """Test dataset loading and preprocessing."""
    print("\n📚 Testing dataset loading...")

    try:
        from itas import DatasetConfig
        from itas.core.dataset_manager import DatasetManager

        # Create a minimal dataset config
        dataset_config = DatasetConfig(
            dataset_name="wikitext",
            dataset_kwargs={"name": "wikitext-2-raw-v1"},
            dataset_split="train",
            text_column="text",
            max_seq_length=512,  # Smaller for testing
            chunk_size=512,
            streaming=False,
            num_proc=1,  # Single process for debugging
            trust_remote_code=False,
        )

        # Load a dummy tokenizer for testing
        from transformers import AutoTokenizer

        tokenizer = AutoTokenizer.from_pretrained("meta-llama/Llama-3.1-8B-Instruct")
        if tokenizer.pad_token is None:
            tokenizer.pad_token = tokenizer.eos_token

        print("  Loading dataset...")
        dataset_manager = DatasetManager(dataset_config, tokenizer)
        dataset = dataset_manager.load_dataset()
        processed_dataset = dataset_manager.preprocess_dataset()

        print(f"✅ Dataset loaded successfully")
        print(f"  Raw size: {len(dataset)}")
        print(f"  Processed size: {len(processed_dataset)}")

        return dataset_manager

    except Exception as e:
        print(f"❌ Dataset loading error: {e}")
        traceback.print_exc()
        return None


def test_activation_streaming(model, tokenizer, dataset_manager):
    """Test activation streaming with minimal setup."""
    print("\n🔄 Testing activation streaming...")

    try:
        from itas import SAEConfig, ModelConfig, DatasetConfig, TrainingConfig
        from itas.core.activations_store import ActivationsStore

        # Create minimal config
        config = SAEConfig(
            model=ModelConfig(
                model_name="meta-llama/Llama-3.1-8B-Instruct",
                use_flash_attention=False,
                torch_dtype="bfloat16",
                device_map="auto",
            ),
            dataset=DatasetConfig(
                dataset_name="wikitext",
                dataset_kwargs={"name": "wikitext-2-raw-v1"},
                dataset_split="train",
                text_column="text",
                max_seq_length=512,
                chunk_size=512,
            ),
            training=TrainingConfig(
                total_training_tokens=1000,  # Very small for testing
                batch_size=64,
                learning_rate=3e-4,
            ),
            hook_layer=16,
            hook_name="layers.16.mlp",
            device="cuda" if torch.cuda.is_available() else "cpu",
        )

        print("  Creating activations store...")
        activations_store = ActivationsStore(model, tokenizer, config, dataset_manager)

        print("  Testing single batch collection...")
        with activations_store:
            # Try to collect just one batch
            activations = activations_store.collect_activations(
                num_batches=1, batch_size=2
            )
            print(f"✅ Activation collection successful")
            print(f"  Activation shape: {activations.shape}")

        return True

    except Exception as e:
        print(f"❌ Activation streaming error: {e}")
        traceback.print_exc()
        return False


def test_minimal_training():
    """Test minimal training setup to identify the exact error."""
    print("\n🏋️ Testing minimal training setup...")

    try:
        from itas import (
            SAETrainer,
            SAEConfig,
            ModelConfig,
            DatasetConfig,
            TrainingConfig,
        )

        # Create minimal training config
        config = SAEConfig(
            model=ModelConfig(
                model_name="meta-llama/Llama-3.1-8B-Instruct",
                use_flash_attention=False,  # Disable for compatibility
                torch_dtype="bfloat16",
                device_map="auto",
            ),
            dataset=DatasetConfig(
                dataset_name="wikitext",
                dataset_kwargs={"name": "wikitext-2-raw-v1"},
                dataset_split="train",
                text_column="text",
                max_seq_length=256,  # Very small
                chunk_size=256,
                streaming=False,
                num_proc=1,
            ),
            training=TrainingConfig(
                total_training_tokens=100,  # Tiny amount for testing
                batch_size=32,
                learning_rate=3e-4,
                l1_coefficient=1e-3,
                log_every_n_steps=1,
                eval_every_n_tokens=50,
                use_wandb=False,
            ),
            architecture="standard",  # Simplest architecture
            expansion_factor=4,  # Small expansion
            hook_layer=16,
            hook_name="layers.16.mlp",
            device="cuda" if torch.cuda.is_available() else "cpu",
            dtype="float32",
        )

        print("  Creating trainer...")
        trainer = SAETrainer(config)

        print("  Starting minimal training...")
        # This should fail quickly and show us the exact error
        sae = trainer.train()

        print("✅ Minimal training completed successfully!")
        return True

    except Exception as e:
        print(f"❌ Training error: {e}")
        print(f"\n🔍 Full error traceback:")
        traceback.print_exc()
        return False


def main():
    """Run comprehensive debugging."""
    print("🚀 Starting comprehensive training error debugging...\n")

    # Test 1: Basic imports
    if not test_basic_imports():
        print("\n❌ Basic imports failed. Please check your installation.")
        return

    # Test 2: Model loading
    model, tokenizer, model_loader = test_model_loading()
    if model is None:
        print("\n❌ Model loading failed. Cannot proceed with further tests.")
        return

    # Test 3: Dataset loading
    dataset_manager = test_dataset_loading()
    if dataset_manager is None:
        print("\n❌ Dataset loading failed. Cannot proceed with activation tests.")
        return

    # Test 4: Activation streaming
    if not test_activation_streaming(model, tokenizer, dataset_manager):
        print(
            "\n❌ Activation streaming failed. This is likely the source of your training error."
        )
        return

    # Test 5: Minimal training
    if not test_minimal_training():
        print("\n❌ Training failed. Check the error traceback above for details.")
        return

    print("\n🎉 All tests passed! The training should work now.")


if __name__ == "__main__":
    main()
